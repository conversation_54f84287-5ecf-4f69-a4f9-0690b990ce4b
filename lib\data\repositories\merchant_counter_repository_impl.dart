import 'dart:convert';
import 'package:dartz/dartz.dart';
import 'package:wesell/core/error/exceptions.dart';
import 'package:wesell/core/error/failures.dart';
import 'package:wesell/data/datasources/merchant_counter_remote_data_source.dart';
import 'package:wesell/data/models/merchant_counter_model.dart';
import 'package:wesell/domain/repositories/merchant_counter_repository.dart';

class MerchantStatsRepositoryImpl implements MerchantStatsRepository {
  final MerchantStatsRemoteDataSource remoteDataSource;

  MerchantStatsRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, MerchantStats>> getMerchantStats(String duration) async {
    try {
      final response = await remoteDataSource.getMerchantStats(duration);
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on AuthenticationException catch (e) {
      return Left(AuthenticationFailure(e.message));
    } on TimeoutException catch (e) {
      return Left(TimeoutFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }
}
