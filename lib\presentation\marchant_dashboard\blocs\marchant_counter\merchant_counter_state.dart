part of 'merchant_counter_bloc.dart';

abstract class MerchantStatsState extends Equatable {
  const MerchantStatsState();

  @override
  List<Object> get props => [];
}

class MerchantStatsInitial extends MerchantStatsState {}

class MerchantStatsLoading extends MerchantStatsState {}

class MerchantStatsLoaded extends MerchantStatsState {
  final MerchantStats stats;

  const MerchantStatsLoaded({required this.stats});

  @override
  List<Object> get props => [stats];
}

class MerchantStatsError extends MerchantStatsState {
  final String message;

  const MerchantStatsError({required this.message});

  @override
  List<Object> get props => [message];
}
