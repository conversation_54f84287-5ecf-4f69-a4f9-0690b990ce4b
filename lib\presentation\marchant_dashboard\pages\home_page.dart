import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/constants/img_string.dart';
import 'package:wesell/core/utils/preferences.dart';
import 'package:wesell/data/models/user_model.dart';
import 'package:wesell/presentation/widgets/custom_dropdown_form_field.dart';
import 'package:wesell/presentation/widgets/dashboard_card.dart';
import '../../../core/localization/app_localizations.dart';
import '../blocs/marchant_counter/merchant_counter_bloc.dart';
import 'package:intl/intl.dart';


class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  User? user;
  ValueNotifier<bool> valueListenable = ValueNotifier(false);
  final List<String> _filterValues = ['Last 7 days','Today','Last month','Last year'];
  String _selectedDuration = 'last 7 days';

  @override
  void initState() {
    super.initState();
    _loadUserData();
     context.read<MerchantStatsBloc>().add(GetMerchantStatsEvent(duration: _selectedDuration));
  }

  Future<void> _loadUserData() async {
    user = await Preferences.getUser() as User;
    valueListenable.value = true;
  }

  String _mapFilterToDuration(String filter) {
    switch (filter) {
      case 'Today':
        return 'last 1 days';
      case 'Last 7 days':
        return 'last 7 days';
      case 'Last month':
        return 'last 1 months';
      case 'Last year':
        return 'last 1 years';
      default:
        return 'last 7 days';
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return ValueListenableBuilder(
      valueListenable: valueListenable,
      builder: (context, value, child) {
        return Scaffold(
          backgroundColor: AppColors.backgroundTheme,
          // extendBodyBehindAppBar: true,
          appBar: AppBar(
            backgroundColor: AppColors.whiteTheme,
            title: Text(localizations.home),
            foregroundColor: Colors.black,
            elevation: 0,),
          body: user?.role != 'merchant' ?  Center(child: Text('Home'))  :dashboardBody() ,
        );
      }
    );
  }

  Widget dashboardBody(){
    return CustomScrollView(
      slivers: [
        SliverAppBar(
        pinned: true,
        flexibleSpace: Container(
          color: AppColors.whiteTheme,
          child: Padding(
            padding: const EdgeInsets.only(right:15.0,left: 15.0,bottom: 10),
            child: CustomDropdownFormField<String>(
              focusedBorderColor: AppColors.borderTheme,
              contentPadding: EdgeInsets.symmetric(vertical: 12,horizontal: 16),
              borderRadius: 8,
              value: _filterValues.firstWhere(
                (filter) => _mapFilterToDuration(filter) == _selectedDuration,
                orElse: () => _filterValues[0],
              ),
              items: _filterValues,
              hintText: AppStrings.selectFilter,
              itemLabel: (item) => item,
              onChanged: (val) {
                final duration = _mapFilterToDuration(val!);
                setState(() => _selectedDuration = duration);
                context.read<MerchantStatsBloc>().add(GetMerchantStatsEvent(duration: duration));
              },
            ),
          ),
        ),
      ),

      SliverToBoxAdapter(
        child: counterCard(),
      ),

      ActivityScreen(activities: activities),
      ],
    );
  }

  Widget counterCard(){
    return BlocBuilder<MerchantStatsBloc, MerchantStatsState>(
      builder: (context, state) {
        if (state is MerchantStatsLoading) {
          return const Center(child: CircularProgressIndicator());
        }
        
        if (state is MerchantStatsError) {
          return Center(child: Text(state.message));
        }

        if (state is MerchantStatsLoaded) {
          final stats = state.stats;
          return SizedBox(
            height: 600,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: GridView.count(
                physics: const NeverScrollableScrollPhysics(),
                // shrinkWrap: true,
                crossAxisCount: 2,
                childAspectRatio: 1.05,
                children: [
                  DashboardStatCard(
                    value: stats.revenue.toString(),
                    label: 'SAR',
                    subtitle: 'Total Revenue',
                    icon: ImageStrings.saru,
                    iconBgColor: AppColors.bageGreen,
                  ),
                  DashboardStatCard(
                    value: stats.offerings.toString(),
                    label: 'Products',
                    subtitle: 'Total Products',
                    icon: ImageStrings.product,
                    iconBgColor: AppColors.bagePink,
                  ),
                  DashboardStatCard(
                    value: stats.postedJobs.toString(),
                    label: 'Jobs',
                    subtitle: 'Total Posted Jobs',
                    icon: ImageStrings.jobsGrid,
                    iconBgColor: AppColors.bageBlue,
                  ),
                  DashboardStatCard(
                    value: stats.sellers.toString(),
                    label: 'Sellers',
                    subtitle: 'Total Sellers',
                    icon: ImageStrings.sellerGrid,
                    iconBgColor: AppColors.bagePurple,
                  ),
                  DashboardStatCard(
                    value: stats.buyers.toString(),
                    label: 'Buyers',
                    subtitle: 'Total Buyers',
                    icon: ImageStrings.buyer,
                    iconBgColor: AppColors.bageTeal,
                  ),
                  DashboardStatCard(
                    value: stats.expenses.toString(),
                    label: 'SAR',
                    subtitle: 'Total Expenses',
                    icon: ImageStrings.sar,
                    iconBgColor: AppColors.bageRed,
                  ),
                ],
              ),
            ),
          );
        }

        return const SizedBox.shrink();
      },
      
    );
  }

  @override
  void dispose() {
    valueListenable.dispose();
    super.dispose();
  }
}



class Activity {
  final String userName;
  final String action;
  final DateTime createdAt;
  final String avatarId;

  Activity({
    required this.userName,
    required this.action,
    required this.createdAt,
    required this.avatarId,
  });
}

class ActivityGroup {
  final String label;
  final List<Activity> activities;

  ActivityGroup({required this.label, required this.activities});
}

class ActivityScreen extends StatelessWidget {
  final List<Activity> activities;

  const ActivityScreen({super.key, required this.activities});

  List<ActivityGroup> groupActivities(List<Activity> activities) {
    final now = DateTime.now();
    final today = now.subtract(Duration(hours: now.hour, minutes: now.minute, seconds: now.second));
    final yesterday = today.subtract(Duration(days: 1));

    List<ActivityGroup> groups = [];

    final todayList = activities.where((a) => a.createdAt.isAfter(today)).toList();
    if (todayList.isNotEmpty) {
      groups.add(ActivityGroup(label: "Today", activities: todayList));
    }

    final yesterdayList = activities
        .where((a) => a.createdAt.isAfter(yesterday) && a.createdAt.isBefore(today))
        .toList();
    if (yesterdayList.isNotEmpty) {
      groups.add(ActivityGroup(label: "Yesterday", activities: yesterdayList));
    }

    final weekAgo = now.subtract(Duration(days: 7));
    final weekList = activities
        .where((a) => a.createdAt.isAfter(weekAgo) && a.createdAt.isBefore(yesterday))
        .toList();
    if (weekList.isNotEmpty) {
      groups.add(ActivityGroup(label: "This Week", activities: weekList));
    }

    final olderList = activities.where((a) => a.createdAt.isBefore(weekAgo)).toList();
    if (olderList.isNotEmpty) {
      groups.add(ActivityGroup(label: "Older", activities: olderList));
    }

    return groups;
  }

  @override
  Widget build(BuildContext context) {
    final grouped = groupActivities(activities);

    return SliverList(
        delegate: SliverChildBuilderDelegate(
          childCount: grouped.length,
        (context, groupIndex) {
          final group = grouped[groupIndex];
          return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Text(
              group.label,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
          for (int i = 0; i < group.activities.length; i++)
            Stack(
              clipBehavior: Clip.none,

              children: [
                // Activity Tile
                Padding(
                  padding: const EdgeInsets.only(left: 16.0, right: 12.0),
                  child: ListTile(
                    contentPadding: const EdgeInsets.only(left: 12, right: 8),
                    leading: CircleAvatar(
                      radius: 14,
                      backgroundImage: NetworkImage(
                        "http://***************/wesell/api/media?fileId=${group.activities[i].avatarId}",
                      ),
                    ),
                    title: Text(group.activities[i].userName ?? ""),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(group.activities[i].action ?? ""),
                        Text(
                          timeAgo(group.activities[i].createdAt),
                          style: const TextStyle(color: Colors.grey, fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ),
                Positioned(
                  left: i != group.activities.length - 1 ? 42 : 0, // aligns with avatar center
                  top: 65,
                  bottom: 0,//i == group.activities.length - 1 ? 40 : 0, // ends the line early on last item
                  child: Container(
                    width: 2,
                    color: i != group.activities.length - 1  ? Colors.grey.shade300: Colors.transparent,
                  ),
                ),
              ],
            ),
        ],
      );
        },)
      );
  }

  String timeAgo(DateTime date) {
    final duration = DateTime.now().difference(date);
    if (duration.inMinutes < 60) return '${duration.inMinutes} minutes ago';
    if (duration.inHours < 24) return '${duration.inHours} hours ago';
    if (duration.inDays < 7) return '${duration.inDays} days ago';
    return DateFormat('dd MMM yyyy').format(date);
  }
}

List<Activity> parseActivitiesFromJson(List<dynamic> jsonRows) {
  return jsonRows.map((item) {
    final doer = item['doer'];
    return Activity(
      userName: "${doer['firstName']} ${doer['lastName']}",
      action: item['action'],
      createdAt: DateTime.parse(item['createdAt']),
      avatarId: doer['avatar'],
    );
  }).toList();
}


var json = {
    "rows": [
        {
            "doer": {
                "firstName": "Wamiq",
                "lastName": "Seller",
                "email": "<EMAIL>",
                "type": "dealMaker",
                "userId": "yjiciovlq3fy5d6p5m00",
                "avatar": "bd3eb7b7-2a21-48f8-848a-9a82eb1f0f80"
            },
            "observers": [
                "4jxvqye8579xlkvsss5r",
                "yjiciovlq3fy5d6p5m00"
            ],
            "entityId": "t36qgz6cavywr4ebm9t7",
            "entityName": "contracts",
            "action": "Contract has been signed",
            "activityId": "fz5bx97r2kfpfswd4z5j",
            "createdAt": "2025-07-22T18:57:27.387Z",
            "updatedAt": "2025-07-22T18:57:27.387Z"
        },
        {
            "doer": {
                "firstName": "Muhammad",
                "lastName": "Wamiq",
                "email": "<EMAIL>",
                "type": "merchant",
                "userId": "4jxvqye8579xlkvsss5r",
                "avatar": "2de6d9f6-b97a-42d8-bc45-e26d18487dad"
            },
            "observers": [
                "4jxvqye8579xlkvsss5r",
                "yjiciovlq3fy5d6p5m00"
            ],
            "entityName": "disputes",
            "action": "Replied on a dispute ticket",
            "activityId": "jnoa4pt8lj4t9t3ggzlf",
            "createdAt": "2025-07-17T13:58:32.334Z",
            "updatedAt": "2025-07-17T13:58:32.334Z"
        },
        {
            "doer": {
                "firstName": "Muhammad",
                "lastName": "Wamiq",
                "email": "<EMAIL>",
                "type": "merchant",
                "userId": "4jxvqye8579xlkvsss5r",
                "avatar": "2de6d9f6-b97a-42d8-bc45-e26d18487dad"
            },
            "observers": [
                "4jxvqye8579xlkvsss5r",
                "yjiciovlq3fy5d6p5m00"
            ],
            "entityId": "t36qgz6cavywr4ebm9t7",
            "entityName": "contracts",
            "action": "Contract has been sent",
            "activityId": "z72h67m2jcfonr6dlp6l",
            "createdAt": "2025-07-17T11:17:00.225Z",
            "updatedAt": "2025-07-17T11:17:00.225Z"
        },
        {
            "doer": {
                "firstName": "Muhammad",
                "lastName": "Wamiq",
                "email": "<EMAIL>",
                "type": "merchant",
                "userId": "4jxvqye8579xlkvsss5r",
                "avatar": "2de6d9f6-b97a-42d8-bc45-e26d18487dad"
            },
            "observers": [
                "4jxvqye8579xlkvsss5r",
                "yjiciovlq3fy5d6p5m00"
            ],
            "entityId": "t36qgz6cavywr4ebm9t7",
            "entityName": "contracts",
            "action": "Contract has been signed",
            "activityId": "8o2wnvw5mu9wdvdfncsd",
            "createdAt": "2025-07-17T11:16:59.746Z",
            "updatedAt": "2025-07-17T11:16:59.746Z"
        },
        {
            "doer": {
                "firstName": "Muhammad",
                "lastName": "Wamiq",
                "email": "<EMAIL>",
                "type": "merchant",
                "userId": "4jxvqye8579xlkvsss5r",
                "avatar": "2de6d9f6-b97a-42d8-bc45-e26d18487dad"
            },
            "observers": [
                "4jxvqye8579xlkvsss5r",
                "yjiciovlq3fy5d6p5m00"
            ],
            "entityId": "t36qgz6cavywr4ebm9t7",
            "entityName": "contracts",
            "action": "New contract has been created",
            "activityId": "blx474zo6kd8kshsynem",
            "createdAt": "2025-07-17T11:16:59.394Z",
            "updatedAt": "2025-07-17T11:16:59.394Z"
        },
        {
            "doer": {
                "firstName": "Muhammad",
                "lastName": "Wamiq",
                "email": "<EMAIL>",
                "type": "merchant",
                "userId": "4jxvqye8579xlkvsss5r",
                "avatar": "2de6d9f6-b97a-42d8-bc45-e26d18487dad"
            },
            "observers": [
                "4jxvqye8579xlkvsss5r",
                "yjiciovlq3fy5d6p5m00"
            ],
            "entityId": "jkyhm0syvqafz2hpvvoy/gjsfag5t36tzya30ob87",
            "entityName": "applications",
            "action": "Job application has been offered",
            "activityId": "m5iln63zufj0n8sks13h",
            "createdAt": "2025-07-17T11:16:58.828Z",
            "updatedAt": "2025-07-17T11:16:58.828Z"
        },
        {
            "doer": {
                "firstName": "Wamiq",
                "lastName": "Seller",
                "email": "<EMAIL>",
                "type": "dealMaker",
                "userId": "yjiciovlq3fy5d6p5m00",
                "avatar": "bd3eb7b7-2a21-48f8-848a-9a82eb1f0f80"
            },
            "observers": [
                "4jxvqye8579xlkvsss5r",
                "yjiciovlq3fy5d6p5m00"
            ],
            "entityId": "jkyhm0syvqafz2hpvvoy",
            "entityName": "jobs",
            "action": "Job has been applied",
            "activityId": "87z0vcx9z60yb2usqs7h",
            "createdAt": "2025-07-17T11:16:09.554Z",
            "updatedAt": "2025-07-17T11:16:09.554Z"
        },
        {
            "doer": {
                "firstName": "Muhammad",
                "lastName": "Wamiq",
                "email": "<EMAIL>",
                "type": "merchant",
                "userId": "4jxvqye8579xlkvsss5r",
                "avatar": "2de6d9f6-b97a-42d8-bc45-e26d18487dad"
            },
            "observers": [
                "4jxvqye8579xlkvsss5r",
                "yjiciovlq3fy5d6p5m00"
            ],
            "entityId": "jkyhm0syvqafz2hpvvoy",
            "entityName": "jobs",
            "action": "Invited to a job",
            "activityId": "18tz7kqvggy61ur4fmfy",
            "createdAt": "2025-07-17T11:15:32.285Z",
            "updatedAt": "2025-07-17T11:15:32.285Z"
        },
        {
            "doer": {
                "firstName": "Muhammad",
                "lastName": "Wamiq",
                "email": "<EMAIL>",
                "type": "merchant",
                "userId": "4jxvqye8579xlkvsss5r",
                "avatar": "2de6d9f6-b97a-42d8-bc45-e26d18487dad"
            },
            "observers": [
                "4jxvqye8579xlkvsss5r"
            ],
            "entityId": "y3fcbekrjoxr4juzdzch",
            "entityName": "jobs",
            "action": "Created a new job",
            "activityId": "4zdxgk7m0xy0ll1nu6r7",
            "createdAt": "2025-07-17T06:56:58.841Z",
            "updatedAt": "2025-07-17T06:56:58.841Z"
        },
        {
            "doer": {
                "firstName": "Faraz ",
                "lastName": "Merchant ",
                "email": "<EMAIL>",
                "type": "merchant",
                "userId": "9twe4dfhv3r7u5upk04r",
                "avatar": "a3474db6-e116-45db-8560-e3e44452f2f1"
            },
            "observers": [
                "4jxvqye8579xlkvsss5r",
                "yjiciovlq3fy5d6p5m00"
            ],
            "entityName": "disputes",
            "action": "Replied on a dispute ticket",
            "activityId": "yvxwhdhnyjo24lfyjs6h",
            "createdAt": "2025-07-16T10:57:14.945Z",
            "updatedAt": "2025-07-16T10:57:14.945Z"
        }
    ],
    "total": 162,
    "size": 10,
    "page": 1
};

final List<Activity> activities = parseActivitiesFromJson((json['rows'] as List));


