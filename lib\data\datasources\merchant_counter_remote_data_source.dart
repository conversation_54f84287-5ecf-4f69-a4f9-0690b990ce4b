import 'package:wesell/core/constants/app_constants.dart';
import 'package:wesell/core/network/dio_client.dart';
import 'package:wesell/data/models/merchant_counter_model.dart';

abstract class MerchantStatsRemoteDataSource {
  Future<MerchantStats> getMerchantStats(String duration);
}

class MerchantStatsRemoteDataSourceImpl implements MerchantStatsRemoteDataSource {
  final DioClient dioClient;

  MerchantStatsRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<MerchantStats> getMerchantStats(String duration) async {
        final response = await dioClient.get(
      AppConstants.merchantCounterEndpoint,
      queryParameters: {'duration': duration},
    );
    return MerchantStats.fromJson(response.data as Map<String, dynamic>);
  }
}