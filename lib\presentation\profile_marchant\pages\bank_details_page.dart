import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/utils/validators.dart';
import 'package:wesell/data/models/profile_marchant_model.dart';
import 'package:wesell/presentation/widgets/custom_button.dart';
import 'package:wesell/presentation/widgets/custom_text_field.dart';
import 'package:wesell/presentation/widgets/file_upload_widget.dart';
import '../bloc/profile_marchant_bloc.dart';
import '../bloc/profile_marchant_event.dart';
import '../bloc/profile_marchant_state.dart';

class BankDetailsPage extends StatefulWidget {
  final ProfileMarchantModel profileData;

  const BankDetailsPage({super.key, required this.profileData});

  @override
  _BankDetailsPageState createState() => _BankDetailsPageState();
}

class _BankDetailsPageState extends State<BankDetailsPage> {
  final _formKey = GlobalKey<FormState>();
  final _ibanController = TextEditingController();
  final _accountNameController = TextEditingController();
  final _bankNameController = TextEditingController();
  
  String? ibanCertificateId;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    final bankDetails = widget.profileData.attributes?.bankDetails;
    if (bankDetails != null) {
      _ibanController.text = bankDetails.iban ?? '';
      _accountNameController.text = bankDetails.accountName ?? '';
      _bankNameController.text = bankDetails.bankName ?? '';
      ibanCertificateId = bankDetails.ibanCertificate;
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  void _handleFormSubmit() {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<ProfileMarchantBloc>().add(UpdateBankDetailsEvent(
        iban: _ibanController.text.trim(),
        accountName: _accountNameController.text.trim(),
        bankName: _bankNameController.text.trim(),
        ibanCertificate: ibanCertificateId ?? '',
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: BlocListener<ProfileMarchantBloc, ProfileMarchantState>(
        listener: (context, state) {
          if (state is UpdateBankDetailsSuccess) {
            _showSnackBar(state.message);
            // Refresh profile data after successful update
            context.read<ProfileMarchantBloc>().add(GetProfileEvent());
          } else if (state is UpdateBankDetailsError) {
            _showSnackBar(state.message);
          }
        },
        child: Scaffold(
          backgroundColor: AppColors.whiteTheme,
          appBar: AppBar(
            backgroundColor: AppColors.whiteTheme,
            title: const Text(AppStrings.bankDetails),
            leading: const BackButton(color: AppColors.blackTextTheme),
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // IBAN Field
                  CustomTextField(
                    controller: _ibanController,
                    labelText: AppStrings.ibanLabel,
                    hintText: AppStrings.ibanHint,
                    validator: (value) => Validators.validateRequired(value,AppStrings.ibanLabel),
                  ),
                  const SizedBox(height: 16),
                  
                  // Account Name Field
                  CustomTextField(
                    controller: _accountNameController,
                    labelText: AppStrings.accountNameLabel,
                    hintText: AppStrings.accountNameHint,
                    validator: (value) => Validators.validateRequired(value,AppStrings.accountNameLabel),
                  ),
                  const SizedBox(height: 16),
                  
                  // Bank Name Field
                  CustomTextField(
                    controller: _bankNameController,
                    labelText: AppStrings.bankNameLabel,
                    hintText: AppStrings.bankNameHint,
                    validator: (value) => Validators.validateRequired(value,AppStrings.bankNameLabel),
                  ),
                  const SizedBox(height: 24),
                  
                  // IBAN Certificate Upload
                  FileUploadWidget(
                    label: AppStrings.attachIbanCertificate,
                    uploadId: 'ibanCertificate', 
                    onFileUploaded: (fileId) {
                      setState(() {
                        ibanCertificateId = fileId == '' ? null : fileId;
                      });
                    },
                    currentFileId: ibanCertificateId,
                  ),
                  const SizedBox(height: 24),
                  
                  // Save Button
                  BlocBuilder<ProfileMarchantBloc, ProfileMarchantState>(
                    builder: (context, state) {
                      return CustomButton(
                        width: double.infinity,
                        backgroundColor: AppColors.primaryTheme,
                        text: AppStrings.saveChanges,
                        onPressed: state is UpdateBankDetailsLoading ? null : _handleFormSubmit,
                        isLoading: state is UpdateBankDetailsLoading,
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _ibanController.dispose();
    _accountNameController.dispose();
    _bankNameController.dispose();
    super.dispose();
  }
}
