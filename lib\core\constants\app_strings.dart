class AppStrings {
  static const String forgotPassword = "Forgot password?";
  static const String forgotPasswordTitle = "Forgot Password";
  static const String forgotPasswordSubtitle = "Enter your email address and we'll send you a link to reset your password.";
  static const String sendResetLink = "Send Reset Link";
  static const String backToLogin = "Back to Login";
  static const String resetEmailSent = "Reset email sent successfully!";
  static const String logIn = "Log in";
  static const String signUp = "Sign Up";
  static const String dontHaveAccount = "Don't have an account yet? ";
  static const String joinAsSellerOrMerchant = 'Join as a Seller or a Merchant';
  static const String merchantTitle = 'I am a Merchant,';
  static const String merchantSubtitle = 'I want to hire sellers';
  static const String sellerTitle = 'I am a seller,';
  static const String sellerSubtitle = 'looking to sell products or services';
  static const String joinAsMerchant = 'Join as Merchant';
  static const String joinAsSeller = 'Join as Seller';
  static const String alreadyHaveAccount = 'Already have an account? ';
  static const String signIn = 'Sign in';
  static const String firstName = 'First name';
  static const String lastName = 'Last name';
  static const String email = 'Email';
  static const String password = 'Password';
  static const String confirmPassword = 'Confirm Password';
  static const String yes = 'Yes, I understand and agree to the';
  static const String termsOfService = ' Wesell Terms of Service';
  static const String createMyAccount = 'Create my account';
  static const String continueMerchant = 'Continue';
  static const String mustAgreeTerms = 'You must agree to the Wesell Terms of Service';
  static const String addCompanyInfo = 'Add your Company Information';
  static const String companyLegalName = 'Company Legal Name';
  static const String selectCompanySize = 'Select Company Size';
  static const String selectFilter = 'Select Filter';
  static const String pleaseSelectCompanySize = 'Please select Company Size';
  static const String crNumber = 'C.R Number';
  static const String companyInvestedCapital = 'Company official invested capital';
  static const String selectUnit = 'Select Unit';
  static const String attachCrDocuments = 'Attach C.R documents';
  static const String attachIbanCertificate = 'IBAN Certificate (optional)';
  static const String attachCompanyLicenseDocuments = 'Attach company license documents';

  //settings
  static const String settings = 'Settings';
  static const String companyDetails = 'Company Details';
  static const String industryDetails = 'Industry Details';
  static const String companyLocation = 'Company Location';
  static const String digitalInformation = 'Digital Information';
  static const String legalDocuments = 'Legal Documents';
  static const String payment = 'Payment';
  static const String subscriptionPlan = 'Subscription Plan';
  static const String inAppCredit = 'In-App credit';
  static const String bankDetails = 'Bank Details';
  static const String profileCompletenessTitle = 'Your company profile completeness!';
  static const String profileCompletenessSubtitle = 'Complete it to unlock WeSell features';
  static const String profile = 'Profile';
  static const String disputes = 'Disputes';
  static const String logout = 'Logout';
  //profile forms
  static const String companyName = 'Company Name';
  static const String briefCompanyDescription = 'Brief Company Description';
  static const String detailedCompanyDescription = 'Detailed Company Description';
  static const String uploadAFile = 'Upload a file';
  static const String fileTypeHint = 'PNG, JPG, GIF up to 10MB';
  static const String saveChanges = 'Save Changes';
  static const String selectImageSource = 'Select Image Source';
  static const String imageUploadSuccess = 'Image uploaded successfully';
  //image upload service
  static const String gallery = 'Gallery';
  static const String camera = 'Camera';
  static const String cancel = 'Cancel';
  static const String onlyJpgJpegPngAllowed = 'Only JPG, JPEG, and PNG images are allowed';
  static const String imageSizeLimit = 'Image size must be less than 10 MB';  
  
  
  // File Upload
  static const String fileUploadSuccess = "File uploaded successfully!";
  static const String fileUploadError = "Failed to upload file";
  static const String fileSizeError = "File size must be less than 10 MB";
  static const String fileFormatError = "Only JPG, PNG, JPEG, GIF, and PDF files are allowed";
  static const String selectFile = "Select File";

  // Industry Details
  static const String pleaseSelectIndustries = 'Please select industries from the dropdown';
  static const String industriesSearch = 'Industries Search';
  static const String selectedIndustries = 'Selected Industries:';
  // Company Locations
  static const String country = 'Country';
  static const String city = 'City';
  static const String address = 'Address';
  static const String poBox = 'PO Box';
  // Digital Information
  static const String enterWebsiteUrl = 'Enter your website URL';
  static const String enterFacebookUrl = 'Enter your Facebook URL';
  static const String enterTwitterUrl = 'Enter your Twitter URL';
  static const String enterLinkedinUrl = 'Enter your LinkedIn URL';
  static const String enterYoutubeUrl = 'Enter your YouTube URL';
  static const String enterTiktokUrl = 'Enter your TikTok URL';
  static const String enterSnapchatUrl = 'Enter your Snapchat URL';
  static const String failedToPickFile = 'Failed to pick file';
  static const String uploading = 'Uploading...';
  static const String fileUploaded = 'File uploaded';
  static const String errorPrefix = 'Error: ';
  static const String ibanLabel = 'IBAN';
  static const String ibanHint = 'Enter your IBAN';
  static const String accountNameLabel = 'Name of Account';
  static const String accountNameHint = 'Enter account name';
  static const String bankNameLabel = 'Bank Name';
  static const String bankNameHint = 'Enter bank name';
}